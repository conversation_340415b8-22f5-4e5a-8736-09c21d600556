"use client"

import { useState, useEffect, useTransition } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ProfilePictureUpload } from "@/components/ProfilePictureUpload"

export default function EditProfilePage() {
  const [user, setUser] = useState<any>(null) // eslint-disable-line @typescript-eslint/no-explicit-any
  const [name, setName] = useState("")
  const [bio, setBio] = useState("")
  const [priceMonthly, setPriceMonthly] = useState("")
  const [avatarUrl, setAvatarUrl] = useState("")
  const [customUrl, setCustomUrl] = useState("")
  const [hideSubscriberCount, setHideSubscriberCount] = useState(false)
  const [monetizationType, setMonetizationType] = useState<'subscription' | 'donations' | 'both'>('subscription')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isPending, startTransition] = useTransition()

  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const loadProfile = async () => {
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
      
      if (authError || !authUser) {
        router.push('/login')
        return
      }

      const { data: profile, error: profileError } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (profileError || !profile) {
        setError("Failed to load profile")
        setLoading(false)
        return
      }

      setUser(profile)
      setName(profile.name || "")
      setBio(profile.bio || "")
      setPriceMonthly(profile.price_monthly ? (profile.price_monthly / 100).toFixed(2) : "")
      setAvatarUrl(profile.profile_picture_url || "")
      setCustomUrl(profile.custom_url || "")
      setHideSubscriberCount(profile.hide_subscriber_count || false)
      setMonetizationType(profile.monetization_type || 'subscription')
      setLoading(false)
    }

    loadProfile()
  }, [router, supabase])

  const handleSave = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    startTransition(async () => {
      try {
        // Validate inputs
        if (!name.trim()) {
          setError("Name is required")
          return
        }

        // Validate custom URL
        if (customUrl.trim()) {
          const urlPattern = /^[a-zA-Z0-9_-]+$/
          if (!urlPattern.test(customUrl.trim()) || customUrl.trim().length < 3 || customUrl.trim().length > 30) {
            setError("Custom URL must be 3-30 characters and contain only letters, numbers, hyphens, and underscores")
            return
          }
        }

        let priceInCents = null
        if (priceMonthly.trim()) {
          const price = parseFloat(priceMonthly)
          if (isNaN(price) || price < 2.99 || price > 50) {
            setError("Price must be between $2.99 and $50.00")
            return
          }
          priceInCents = Math.round(price * 100)
        }

        const updateData: any = { // eslint-disable-line @typescript-eslint/no-explicit-any
          name: name.trim(),
          bio: bio.trim() || null,
          profile_picture_url: avatarUrl.trim() || null,
          custom_url: customUrl.trim() || null,
          hide_subscriber_count: hideSubscriberCount,
          monetization_type: monetizationType,
        }

        // All users can set pricing (unified flow)
        updateData.price_monthly = priceInCents

        const { error: updateError } = await supabase
          .from("users")
          .update(updateData)
          .eq("id", user.id)

        if (updateError) {
          setError("Failed to update profile")
          return
        }

        setSuccess("Profile updated successfully!")
        
        // Update local user state
        setUser({ ...user, ...updateData })

        // Redirect to profile after 1 second
        setTimeout(() => {
          router.push(`/u/${user.id}`)
        }, 1000)

      } catch {
        setError("An unexpected error occurred")
      }
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-600">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-8">
      <div className="max-w-2xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Profile</h1>
            <p className="text-gray-600 mt-1">
              Update your profile information
            </p>
          </div>
          <Link
            href={`/u/${user.id}`}
            className="text-gray-600 hover:text-gray-800 font-medium"
          >
            ← Back to Profile
          </Link>
        </div>

        {/* Profile Form */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <form onSubmit={handleSave} className="space-y-6">
            {/* Profile Picture */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Profile Picture
              </label>
              <ProfilePictureUpload
                currentPictureUrl={avatarUrl}
                onPictureChange={(url) => setAvatarUrl(url)}
                userId={user.id}
              />
            </div>

            {/* Basic Info */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Full Name
              </label>
              <input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
                placeholder="Your full name"
              />
            </div>

            <div>
              <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-2">
                Bio (shown on your profile)
              </label>
              <textarea
                id="bio"
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                rows={4}
                maxLength={500}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
                placeholder="Tell readers about yourself and your writing..."
              />
              <p className="text-xs text-gray-500 mt-1">{bio.length}/500 characters</p>
            </div>

            <div>
              <label htmlFor="customUrl" className="block text-sm font-medium text-gray-700 mb-2">
                Custom Profile URL
              </label>
              <div className="flex items-center">
                <span className="text-gray-500 text-sm mr-2">onlydiary.app/</span>
                <input
                  id="customUrl"
                  type="text"
                  value={customUrl}
                  onChange={(e) => setCustomUrl(e.target.value.toLowerCase().replace(/[^a-z0-9_-]/g, ''))}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
                  placeholder="your-name"
                  maxLength={30}
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                3-30 characters. Letters, numbers, hyphens, and underscores only.
                {customUrl && (
                  <span className="block mt-1 text-blue-600">
                    Your URL: onlydiary.app/{customUrl}
                  </span>
                )}
              </p>
            </div>

            {/* Pricing settings - available to all users */}
            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                Subscription Price (per 30 posts)
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2 text-gray-500">$</span>
                <input
                  id="price"
                  type="number"
                  min="2.99"
                  max="50.00"
                  step="0.01"
                  value={priceMonthly}
                  onChange={(e) => setPriceMonthly(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent"
                  placeholder="9.99"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Minimum $2.99, maximum $50.00 per 30 posts
              </p>
            </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Monetization Model
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="monetization"
                        value="subscription"
                        checked={monetizationType === 'subscription'}
                        onChange={(e) => setMonetizationType(e.target.value as 'subscription')}
                        className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        Subscription only - Readers pay for 30 posts
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="monetization"
                        value="donations"
                        checked={monetizationType === 'donations'}
                        onChange={(e) => setMonetizationType(e.target.value as 'donations')}
                        className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        Donations only - Free access with optional tips
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="monetization"
                        value="both"
                        checked={monetizationType === 'both'}
                        onChange={(e) => setMonetizationType(e.target.value as 'both')}
                        className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        Both - Subscriptions and donations
                      </span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    id="hideSubscribers"
                    type="checkbox"
                    checked={hideSubscriberCount}
                    onChange={(e) => setHideSubscriberCount(e.target.checked)}
                    className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded"
                  />
                  <label htmlFor="hideSubscribers" className="ml-2 block text-sm text-gray-700">
                    Hide subscriber count from public profile
                  </label>
                </div>
              </>
            )}

            {error && (
              <div className="text-red-600 text-sm font-medium bg-red-50 p-3 rounded-lg">
                {error}
              </div>
            )}

            {success && (
              <div className="text-green-600 text-sm font-medium bg-green-50 p-3 rounded-lg">
                {success}
              </div>
            )}

            <Button
              type="submit"
              isLoading={isPending}
              className="w-full bg-gray-800 text-white hover:bg-gray-700"
            >
              Update Profile
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
}
